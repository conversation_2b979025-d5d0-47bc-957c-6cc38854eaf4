# Fantasy
#### Fantasy是基于.NET的高性能网络开发框架，支持主流协议，前后端分离。
#### 支持Unity/Godot/WinFrom/WPF/Console等C#客户端接入。
#### 框架支持TCP\KCP\WebSocket\http（支持Unity发布成H5）三种网络协议。 
#### 适合需要快速上手、可扩展、分布式全平台商业级解决方案的框架。
## 导航
* [Fantasy介绍网站](https://www.code-fantasy.com/)
* [Fantasy的API文档](https://www.code-fantasy.com/doc/api/Fantasy.html)
* [入门视频观看地址](https://space.bilibili.com/382126312)
## 快速上手
* 01.快速入门
  * [1.1.获得Fantasy](https://www.code-fantasy.com/top/download-fantasy/)
  * [1.2.配置文件](https://www.code-fantasy.com/top/config-file/)
  * [1.3.Fantasy的网络](https://www.code-fantasy.com/top/use-network/)
  * [1.4.Fantasy的配置文件](https://www.code-fantasy.com/top/config-file/)
  * [1.5.Fantasy的命令行参数](https://www.code-fantasy.com/top/command-line-parameter/)
  * [1.6.如何升级到最新版](https://www.code-fantasy.com/top/upgrade/)
* 02.网络通信
  * [2.1.配置网络协议](https://www.code-fantasy.com/network/network-protocols/)
  * [2.2.客户端服务器之间发送消息](https://www.code-fantasy.com/network/session/)
  * [2.3.服务器之间发送消息](https://www.code-fantasy.com/network/networkmessagingomponent/)
  * [2.4.Route通信协议](https://www.code-fantasy.com/network/network-route/)
  * [2.5.Addressable通信协议](https://www.code-fantasy.com/network/network-addressable/)
* 03.系统组件
  * [3.1.ECS系统](https://www.code-fantasy.com/core/ecs/)
  * [3.2.事件系统](https://www.code-fantasy.com/core/event/)
  * [3.3.任务系统](https://www.code-fantasy.com/core/task/)
  * [3.4.异步协程锁](https://www.code-fantasy.com/core/lock/)
  * [3.5.数据库](https://www.code-fantasy.com/core/db/)
* [更新日志](https://www.code-fantasy.com/changelog/)
* [API文档](https://www.code-fantasy.com/doc/api/Fantasy.html)
* [常见问题](https://www.code-fantasy.com/question/)

## 优质开源项目推荐
#### <a href="https://github.com/egametang/ET"><strong>ET</strong></a> - ET框架是一整套完善的游戏开发框架。
#### <a href="https://github.com/ALEXTANGXIAO/TEngine"><strong>TEngine</strong></a> - TEngine是一个简单(新手友好开箱即用)且强大的Unity框架全平台解决方案。
#### <a href="https://github.com/FlameskyDexive/Legends-Of-Heroes"><strong>Legends-Of-Heroes</strong></a> - 一个LOL风格的球球大作战游戏，基于ET，使用状态同步。
## 交流与讨论：  
__讨论QQ群 : Fantasy服务器开发交流群 569888673 __
## 特别鸣谢

感谢JetBrains公司提供的使用许可证！

<p><a href="https://www.jetbrains.com/?from=fantasy">
<img src="https://user-images.githubusercontent.com/8274346/223466125-611c027a-61f3-4ea0-a96d-4052283da746.png" alt="JetBrains的Logo" width="20%" height="20%"></a></p>
